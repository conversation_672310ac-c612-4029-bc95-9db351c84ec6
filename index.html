<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asteroid Space Shooter</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="ui-overlay">
            <div class="score">Score: <span id="score">0</span></div>
            <div class="lives">Lives: <span id="lives">3</span></div>
            <div class="level">Level: <span id="level">1</span></div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="game-over" id="gameOver" style="display: none;">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <button onclick="restartGame()">Play Again</button>
        </div>
        
        <div class="start-screen" id="startScreen">
            <h1>Asteroid Space Shooter</h1>
            <div class="instructions">
                <h3>Controls:</h3>
                <p>↑ Arrow Key - Thrust</p>
                <p>← → Arrow Keys - Rotate</p>
                <p>Spacebar - Shoot</p>
                <p>P - Pause</p>
            </div>
            <button onclick="startGame()">Start Game</button>
        </div>
        
        <div class="pause-screen" id="pauseScreen" style="display: none;">
            <h2>Game Paused</h2>
            <p>Press P to continue</p>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
