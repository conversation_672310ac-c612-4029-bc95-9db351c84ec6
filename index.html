<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Era - C# Developer</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="hero">
            <div class="hero-content">
                <div class="profile-image">
                    <div class="image-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <h1 class="name">Era</h1>
                <h2 class="title">Senior C# Developer</h2>
                <p class="tagline">Crafting robust solutions with .NET excellence</p>
                <div class="social-links">
                    <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-stack-overflow"></i></a>
                </div>
            </div>
        </header>

        <!-- About Section -->
        <section class="about">
            <div class="section-content">
                <h3 class="section-title">My Journey</h3>
                <div class="story">
                    <p>
                        My journey into the world of C# began during my computer science studies, where I first encountered 
                        the elegance of object-oriented programming. What started as curiosity about how software could 
                        solve real-world problems quickly evolved into a passion for building scalable, maintainable applications.
                    </p>
                    <p>
                        Over the past <span class="highlight">8+ years</span>, I've had the privilege of working across 
                        diverse industries - from fintech startups where milliseconds matter, to enterprise healthcare 
                        systems where reliability is paramount. Each project has taught me that great code isn't just 
                        about syntax; it's about understanding business needs and translating them into robust solutions.
                    </p>
                    <p>
                        I believe in the power of clean architecture, test-driven development, and continuous learning. 
                        Whether I'm optimizing database queries, designing microservices, or mentoring junior developers, 
                        I approach every challenge with the same philosophy: <em>write code that tells a story</em>.
                    </p>
                </div>
            </div>
        </section>

        <!-- Skills Section -->
        <section class="skills">
            <div class="section-content">
                <h3 class="section-title">Technical Expertise</h3>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h4><i class="fas fa-code"></i> Core Technologies</h4>
                        <div class="skill-tags">
                            <span class="skill-tag primary">C# / .NET</span>
                            <span class="skill-tag primary">ASP.NET Core</span>
                            <span class="skill-tag primary">Entity Framework</span>
                            <span class="skill-tag">LINQ</span>
                            <span class="skill-tag">WPF</span>
                            <span class="skill-tag">Blazor</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4><i class="fas fa-database"></i> Data & Storage</h4>
                        <div class="skill-tags">
                            <span class="skill-tag primary">SQL Server</span>
                            <span class="skill-tag">PostgreSQL</span>
                            <span class="skill-tag">MongoDB</span>
                            <span class="skill-tag">Redis</span>
                            <span class="skill-tag">Azure SQL</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4><i class="fas fa-cloud"></i> Cloud & DevOps</h4>
                        <div class="skill-tags">
                            <span class="skill-tag primary">Azure</span>
                            <span class="skill-tag">Docker</span>
                            <span class="skill-tag">Kubernetes</span>
                            <span class="skill-tag">CI/CD</span>
                            <span class="skill-tag">Git</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4><i class="fas fa-cogs"></i> Architecture & Patterns</h4>
                        <div class="skill-tags">
                            <span class="skill-tag primary">Microservices</span>
                            <span class="skill-tag primary">Clean Architecture</span>
                            <span class="skill-tag">DDD</span>
                            <span class="skill-tag">CQRS</span>
                            <span class="skill-tag">Event Sourcing</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Experience Highlights -->
        <section class="experience">
            <div class="section-content">
                <h3 class="section-title">Notable Achievements</h3>
                <div class="achievements">
                    <div class="achievement">
                        <div class="achievement-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="achievement-content">
                            <h4>Performance Optimization</h4>
                            <p>Reduced API response times by 75% through strategic caching and database optimization in a high-traffic e-commerce platform.</p>
                        </div>
                    </div>
                    
                    <div class="achievement">
                        <div class="achievement-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="achievement-content">
                            <h4>Security Enhancement</h4>
                            <p>Implemented comprehensive security measures including OAuth 2.0, JWT tokens, and data encryption for a financial services application.</p>
                        </div>
                    </div>
                    
                    <div class="achievement">
                        <div class="achievement-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="achievement-content">
                            <h4>Team Leadership</h4>
                            <p>Led a team of 6 developers in migrating a legacy system to .NET 6, improving maintainability and reducing technical debt by 60%.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Philosophy Section -->
        <section class="philosophy">
            <div class="section-content">
                <h3 class="section-title">Development Philosophy</h3>
                <div class="philosophy-grid">
                    <div class="philosophy-item">
                        <i class="fas fa-lightbulb"></i>
                        <h4>Clean Code</h4>
                        <p>Code should be readable, maintainable, and self-documenting. Every line should have a purpose.</p>
                    </div>
                    
                    <div class="philosophy-item">
                        <i class="fas fa-sync-alt"></i>
                        <h4>Continuous Learning</h4>
                        <p>Technology evolves rapidly. Staying current with .NET updates and industry best practices is essential.</p>
                    </div>
                    
                    <div class="philosophy-item">
                        <i class="fas fa-handshake"></i>
                        <h4>Collaboration</h4>
                        <p>Great software is built by great teams. Communication and knowledge sharing drive success.</p>
                    </div>
                    
                    <div class="philosophy-item">
                        <i class="fas fa-target"></i>
                        <h4>Business Focus</h4>
                        <p>Technical excellence must align with business objectives to create meaningful value.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact">
            <div class="section-content">
                <h3 class="section-title">Let's Connect</h3>
                <p class="contact-text">
                    I'm always interested in discussing new opportunities, challenging projects, 
                    or simply connecting with fellow developers. Whether you're looking for a 
                    seasoned C# developer or want to share insights about the latest .NET features, 
                    I'd love to hear from you.
                </p>
                <div class="contact-buttons">
                    <a href="mailto:<EMAIL>" class="btn btn-primary">
                        <i class="fas fa-envelope"></i> Get In Touch
                    </a>
                    <a href="#" class="btn btn-secondary">
                        <i class="fas fa-download"></i> Download Resume
                    </a>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Era. Built with passion for clean code and elegant solutions.</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
