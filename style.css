* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #000;
    color: #fff;
    font-family: 'Courier New', monospace;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.game-container {
    position: relative;
    background: radial-gradient(circle, #001122 0%, #000000 100%);
    border: 2px solid #333;
    border-radius: 10px;
    overflow: hidden;
}

#gameCanvas {
    display: block;
    background: transparent;
}

.ui-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.score, .lives, .level {
    background: rgba(0, 0, 0, 0.7);
    padding: 8px 12px;
    border-radius: 5px;
    border: 1px solid #333;
    font-size: 16px;
    font-weight: bold;
}

.start-screen, .game-over, .pause-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 20;
    text-align: center;
}

.start-screen h1 {
    font-size: 3em;
    margin-bottom: 30px;
    color: #00ff88;
    text-shadow: 0 0 20px #00ff88;
}

.instructions {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #333;
    border-radius: 10px;
    background: rgba(0, 20, 40, 0.8);
}

.instructions h3 {
    margin-bottom: 15px;
    color: #00ccff;
}

.instructions p {
    margin: 8px 0;
    font-size: 14px;
}

button {
    background: linear-gradient(45deg, #00ff88, #00ccff);
    border: none;
    padding: 15px 30px;
    font-size: 18px;
    font-weight: bold;
    color: #000;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.game-over h2, .pause-screen h2 {
    font-size: 2.5em;
    margin-bottom: 20px;
    color: #ff4444;
    text-shadow: 0 0 15px #ff4444;
}

.pause-screen h2 {
    color: #ffaa00;
    text-shadow: 0 0 15px #ffaa00;
}

#finalScore {
    color: #00ff88;
    font-size: 1.2em;
}

/* Responsive design */
@media (max-width: 850px) {
    .game-container {
        transform: scale(0.8);
    }
}

@media (max-width: 680px) {
    .game-container {
        transform: scale(0.6);
    }
    
    .start-screen h1 {
        font-size: 2em;
    }
    
    .instructions {
        padding: 15px;
    }
}
