/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.profile-image {
    margin-bottom: 30px;
}

.image-placeholder {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60px;
    color: white;
    border: 5px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.image-placeholder:hover {
    transform: scale(1.05);
}

.name {
    font-size: 3.5em;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title {
    font-size: 1.8em;
    font-weight: 300;
    margin-bottom: 15px;
    color: #ecf0f1;
}

.tagline {
    font-size: 1.2em;
    color: #bdc3c7;
    margin-bottom: 30px;
    font-style: italic;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    text-decoration: none;
    font-size: 20px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-content {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 40px;
}

.section-title {
    font-size: 2.5em;
    text-align: center;
    margin-bottom: 50px;
    color: #2c3e50;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* About Section */
.about {
    background: #f8f9fa;
}

.story p {
    font-size: 1.1em;
    margin-bottom: 25px;
    text-align: justify;
    color: #555;
}

.highlight {
    color: #667eea;
    font-weight: 600;
}

/* Skills Section */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 40px;
}

.skill-category h4 {
    font-size: 1.3em;
    margin-bottom: 20px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.skill-category i {
    color: #667eea;
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.skill-tag {
    background: #e9ecef;
    color: #495057;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9em;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skill-tag.primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.skill-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Experience Section */
.experience {
    background: #f8f9fa;
}

.achievements {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 40px;
}

.achievement {
    display: flex;
    gap: 20px;
    padding: 30px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.achievement:hover {
    transform: translateY(-5px);
}

.achievement-icon {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.achievement-content h4 {
    font-size: 1.3em;
    margin-bottom: 10px;
    color: #2c3e50;
}

.achievement-content p {
    color: #666;
    line-height: 1.6;
}

/* Philosophy Section */
.philosophy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.philosophy-item {
    text-align: center;
    padding: 30px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.philosophy-item:hover {
    transform: translateY(-5px);
}

.philosophy-item i {
    font-size: 3em;
    color: #667eea;
    margin-bottom: 20px;
}

.philosophy-item h4 {
    font-size: 1.3em;
    margin-bottom: 15px;
    color: #2c3e50;
}

.philosophy-item p {
    color: #666;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    background: #f8f9fa;
    text-align: center;
}

.contact-text {
    font-size: 1.1em;
    color: #666;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.contact-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-secondary {
    background: transparent;
    color: #667eea;
    border-color: #667eea;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 30px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        padding: 60px 20px;
    }
    
    .name {
        font-size: 2.5em;
    }
    
    .section-content {
        padding: 0 20px;
    }
    
    .skills-grid,
    .achievements,
    .philosophy-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .achievement {
        flex-direction: column;
        text-align: center;
    }
}
