// Game variables
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

let gameState = 'start'; // 'start', 'playing', 'paused', 'gameOver'
let score = 0;
let lives = 3;
let level = 1;
let gameRunning = false;

// Game objects
let player = {};
let bullets = [];
let asteroids = [];
let particles = [];

// Input handling
const keys = {};

// Game settings
const PLAYER_SIZE = 10;
const BULLET_SPEED = 8;
const BULLET_LIFETIME = 60;
const ASTEROID_SPEEDS = [0.5, 1, 1.5, 2];
const PARTICLE_LIFETIME = 30;

// Initialize player
function initPlayer() {
    player = {
        x: canvas.width / 2,
        y: canvas.height / 2,
        angle: 0,
        velocity: { x: 0, y: 0 },
        thrust: false,
        invulnerable: 0
    };
}

// Initialize asteroids for level
function initAsteroids() {
    asteroids = [];
    const numAsteroids = 4 + level;
    
    for (let i = 0; i < numAsteroids; i++) {
        createAsteroid('large');
    }
}

// Create asteroid
function createAsteroid(size, x, y) {
    const asteroid = {
        size: size,
        x: x || Math.random() * canvas.width,
        y: y || Math.random() * canvas.height,
        velocity: {
            x: (Math.random() - 0.5) * ASTEROID_SPEEDS[level - 1] * 2,
            y: (Math.random() - 0.5) * ASTEROID_SPEEDS[level - 1] * 2
        },
        angle: Math.random() * Math.PI * 2,
        rotation: (Math.random() - 0.5) * 0.1,
        vertices: generateAsteroidVertices()
    };
    
    // Set size properties
    switch (size) {
        case 'large':
            asteroid.radius = 40;
            asteroid.points = 20;
            break;
        case 'medium':
            asteroid.radius = 25;
            asteroid.points = 10;
            break;
        case 'small':
            asteroid.radius = 15;
            asteroid.points = 5;
            break;
    }
    
    // Ensure asteroid doesn't spawn too close to player
    if (x === undefined && y === undefined) {
        const dist = Math.sqrt((asteroid.x - player.x) ** 2 + (asteroid.y - player.y) ** 2);
        if (dist < 100) {
            asteroid.x = player.x + (Math.random() > 0.5 ? 150 : -150);
            asteroid.y = player.y + (Math.random() > 0.5 ? 150 : -150);
        }
    }
    
    asteroids.push(asteroid);
}

// Generate random asteroid shape
function generateAsteroidVertices() {
    const vertices = [];
    const numVertices = 8 + Math.floor(Math.random() * 4);
    
    for (let i = 0; i < numVertices; i++) {
        const angle = (i / numVertices) * Math.PI * 2;
        const radius = 0.8 + Math.random() * 0.4; // Random radius variation
        vertices.push({ angle, radius });
    }
    
    return vertices;
}

// Create bullet
function createBullet() {
    const bullet = {
        x: player.x + Math.cos(player.angle) * PLAYER_SIZE,
        y: player.y + Math.sin(player.angle) * PLAYER_SIZE,
        velocity: {
            x: Math.cos(player.angle) * BULLET_SPEED + player.velocity.x,
            y: Math.sin(player.angle) * BULLET_SPEED + player.velocity.y
        },
        lifetime: BULLET_LIFETIME
    };
    
    bullets.push(bullet);
}

// Create particle effect
function createParticles(x, y, count, color) {
    for (let i = 0; i < count; i++) {
        particles.push({
            x: x,
            y: y,
            velocity: {
                x: (Math.random() - 0.5) * 6,
                y: (Math.random() - 0.5) * 6
            },
            lifetime: PARTICLE_LIFETIME,
            maxLifetime: PARTICLE_LIFETIME,
            color: color || '#ffaa00'
        });
    }
}

// Event listeners
document.addEventListener('keydown', (e) => {
    keys[e.code] = true;
    
    if (e.code === 'Space') {
        e.preventDefault();
        if (gameState === 'playing') {
            createBullet();
        }
    }
    
    if (e.code === 'KeyP') {
        e.preventDefault();
        if (gameState === 'playing') {
            pauseGame();
        } else if (gameState === 'paused') {
            resumeGame();
        }
    }
});

document.addEventListener('keyup', (e) => {
    keys[e.code] = false;
});

// Game control functions
function startGame() {
    gameState = 'playing';
    gameRunning = true;
    score = 0;
    lives = 3;
    level = 1;
    
    document.getElementById('startScreen').style.display = 'none';
    document.getElementById('gameOver').style.display = 'none';
    
    initPlayer();
    initAsteroids();
    bullets = [];
    particles = [];
    
    updateUI();
    gameLoop();
}

function pauseGame() {
    gameState = 'paused';
    document.getElementById('pauseScreen').style.display = 'flex';
}

function resumeGame() {
    gameState = 'playing';
    document.getElementById('pauseScreen').style.display = 'none';
}

function gameOver() {
    gameState = 'gameOver';
    gameRunning = false;
    document.getElementById('finalScore').textContent = score;
    document.getElementById('gameOver').style.display = 'flex';
}

function restartGame() {
    startGame();
}

function updateUI() {
    document.getElementById('score').textContent = score;
    document.getElementById('lives').textContent = lives;
    document.getElementById('level').textContent = level;
}

// Physics and collision functions
function wrapPosition(obj) {
    if (obj.x < 0) obj.x = canvas.width;
    if (obj.x > canvas.width) obj.x = 0;
    if (obj.y < 0) obj.y = canvas.height;
    if (obj.y > canvas.height) obj.y = 0;
}

function checkCollision(obj1, obj2, radius1, radius2) {
    const dx = obj1.x - obj2.x;
    const dy = obj1.y - obj2.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    return distance < (radius1 + radius2);
}

// Update functions
function updatePlayer() {
    // Handle input
    if (keys['ArrowLeft']) {
        player.angle -= 0.15;
    }
    if (keys['ArrowRight']) {
        player.angle += 0.15;
    }

    player.thrust = keys['ArrowUp'];

    // Apply thrust
    if (player.thrust) {
        const thrustPower = 0.3;
        player.velocity.x += Math.cos(player.angle) * thrustPower;
        player.velocity.y += Math.sin(player.angle) * thrustPower;

        // Create thrust particles
        if (Math.random() < 0.3) {
            createParticles(
                player.x - Math.cos(player.angle) * PLAYER_SIZE,
                player.y - Math.sin(player.angle) * PLAYER_SIZE,
                1,
                '#ff6600'
            );
        }
    }

    // Apply friction
    player.velocity.x *= 0.99;
    player.velocity.y *= 0.99;

    // Limit max speed
    const maxSpeed = 8;
    const speed = Math.sqrt(player.velocity.x ** 2 + player.velocity.y ** 2);
    if (speed > maxSpeed) {
        player.velocity.x = (player.velocity.x / speed) * maxSpeed;
        player.velocity.y = (player.velocity.y / speed) * maxSpeed;
    }

    // Update position
    player.x += player.velocity.x;
    player.y += player.velocity.y;

    // Wrap around screen
    wrapPosition(player);

    // Update invulnerability
    if (player.invulnerable > 0) {
        player.invulnerable--;
    }
}

function updateBullets() {
    for (let i = bullets.length - 1; i >= 0; i--) {
        const bullet = bullets[i];

        // Update position
        bullet.x += bullet.velocity.x;
        bullet.y += bullet.velocity.y;

        // Wrap around screen
        wrapPosition(bullet);

        // Update lifetime
        bullet.lifetime--;

        // Remove expired bullets
        if (bullet.lifetime <= 0) {
            bullets.splice(i, 1);
        }
    }
}

function updateAsteroids() {
    for (let asteroid of asteroids) {
        // Update position
        asteroid.x += asteroid.velocity.x;
        asteroid.y += asteroid.velocity.y;

        // Update rotation
        asteroid.angle += asteroid.rotation;

        // Wrap around screen
        wrapPosition(asteroid);
    }
}

function updateParticles() {
    for (let i = particles.length - 1; i >= 0; i--) {
        const particle = particles[i];

        // Update position
        particle.x += particle.velocity.x;
        particle.y += particle.velocity.y;

        // Apply friction
        particle.velocity.x *= 0.95;
        particle.velocity.y *= 0.95;

        // Update lifetime
        particle.lifetime--;

        // Remove expired particles
        if (particle.lifetime <= 0) {
            particles.splice(i, 1);
        }
    }
}

// Collision detection
function checkCollisions() {
    // Bullet-asteroid collisions
    for (let i = bullets.length - 1; i >= 0; i--) {
        const bullet = bullets[i];

        for (let j = asteroids.length - 1; j >= 0; j--) {
            const asteroid = asteroids[j];

            if (checkCollision(bullet, asteroid, 2, asteroid.radius)) {
                // Remove bullet
                bullets.splice(i, 1);

                // Create explosion particles
                createParticles(asteroid.x, asteroid.y, 8, '#ffaa00');

                // Add score
                score += asteroid.points;

                // Split asteroid
                const asteroidX = asteroid.x;
                const asteroidY = asteroid.y;
                asteroids.splice(j, 1);

                if (asteroid.size === 'large') {
                    createAsteroid('medium', asteroidX + 20, asteroidY);
                    createAsteroid('medium', asteroidX - 20, asteroidY);
                } else if (asteroid.size === 'medium') {
                    createAsteroid('small', asteroidX + 15, asteroidY);
                    createAsteroid('small', asteroidX - 15, asteroidY);
                }

                break;
            }
        }
    }

    // Player-asteroid collisions
    if (player.invulnerable === 0) {
        for (let asteroid of asteroids) {
            if (checkCollision(player, asteroid, PLAYER_SIZE, asteroid.radius)) {
                // Player hit
                lives--;
                player.invulnerable = 120; // 2 seconds of invulnerability

                // Create explosion particles
                createParticles(player.x, player.y, 12, '#ff4444');

                // Reset player position and velocity
                player.x = canvas.width / 2;
                player.y = canvas.height / 2;
                player.velocity.x = 0;
                player.velocity.y = 0;

                if (lives <= 0) {
                    gameOver();
                    return;
                }

                updateUI();
                break;
            }
        }
    }

    // Check if level complete
    if (asteroids.length === 0) {
        level++;
        initAsteroids();
        updateUI();

        // Bonus points for completing level
        score += level * 100;
        updateUI();
    }
}

// Rendering functions
function drawPlayer() {
    ctx.save();
    ctx.translate(player.x, player.y);
    ctx.rotate(player.angle);

    // Make player blink when invulnerable
    if (player.invulnerable === 0 || Math.floor(player.invulnerable / 5) % 2 === 0) {
        ctx.strokeStyle = '#00ff88';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(PLAYER_SIZE, 0);
        ctx.lineTo(-PLAYER_SIZE, -PLAYER_SIZE / 2);
        ctx.lineTo(-PLAYER_SIZE / 2, 0);
        ctx.lineTo(-PLAYER_SIZE, PLAYER_SIZE / 2);
        ctx.closePath();
        ctx.stroke();

        // Draw thrust
        if (player.thrust) {
            ctx.strokeStyle = '#ff6600';
            ctx.beginPath();
            ctx.moveTo(-PLAYER_SIZE, -3);
            ctx.lineTo(-PLAYER_SIZE - 8, 0);
            ctx.lineTo(-PLAYER_SIZE, 3);
            ctx.stroke();
        }
    }

    ctx.restore();
}

function drawBullets() {
    ctx.fillStyle = '#ffffff';
    for (let bullet of bullets) {
        ctx.beginPath();
        ctx.arc(bullet.x, bullet.y, 2, 0, Math.PI * 2);
        ctx.fill();
    }
}

function drawAsteroids() {
    for (let asteroid of asteroids) {
        ctx.save();
        ctx.translate(asteroid.x, asteroid.y);
        ctx.rotate(asteroid.angle);

        ctx.strokeStyle = '#cccccc';
        ctx.lineWidth = 2;
        ctx.beginPath();

        for (let i = 0; i < asteroid.vertices.length; i++) {
            const vertex = asteroid.vertices[i];
            const x = Math.cos(vertex.angle) * vertex.radius * asteroid.radius;
            const y = Math.sin(vertex.angle) * vertex.radius * asteroid.radius;

            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }

        ctx.closePath();
        ctx.stroke();
        ctx.restore();
    }
}

function drawParticles() {
    for (let particle of particles) {
        const alpha = particle.lifetime / particle.maxLifetime;
        ctx.fillStyle = particle.color + Math.floor(alpha * 255).toString(16).padStart(2, '0');

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, 2, 0, Math.PI * 2);
        ctx.fill();
    }
}

function drawStars() {
    ctx.fillStyle = '#ffffff';
    for (let i = 0; i < 100; i++) {
        const x = (i * 37) % canvas.width;
        const y = (i * 73) % canvas.height;
        const size = (i % 3) * 0.5 + 0.5;

        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();
    }
}

function render() {
    // Clear canvas
    ctx.fillStyle = '#000011';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw background stars
    drawStars();

    // Draw game objects
    drawParticles();
    drawAsteroids();
    drawBullets();
    drawPlayer();
}

// Main game loop
function gameLoop() {
    if (!gameRunning) return;

    if (gameState === 'playing') {
        // Update game objects
        updatePlayer();
        updateBullets();
        updateAsteroids();
        updateParticles();

        // Check collisions
        checkCollisions();
    }

    // Render everything
    render();

    // Continue loop
    requestAnimationFrame(gameLoop);
}

// Initialize the game
function init() {
    // Show start screen
    document.getElementById('startScreen').style.display = 'flex';
}

// Start the game when page loads
window.addEventListener('load', init);
